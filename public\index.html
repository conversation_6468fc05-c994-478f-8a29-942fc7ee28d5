<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerador de Contratos PDF</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        textarea {
            height: 100px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .btn {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            display: none;
        }

        .section-title {
            background: #e9ecef;
            padding: 10px;
            margin: 20px 0 15px 0;
            border-radius: 5px;
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Gerador de Contratos PDF</h1>

        <form id="contratoForm">
            <!-- DADOS DA CONTRATADA -->
            <div class="section-title">📋 Dados da Contratada</div>

            <div class="form-row">
                <div class="form-group">
                    <label for="nomeContratada">Nome da Empresa:</label>
                    <input type="text" id="nomeContratada" name="nomeContratada" required>
                </div>
                <div class="form-group">
                    <label for="nomeFantasia">Nome Fantasia:</label>
                    <input type="text" id="nomeFantasia" name="nomeFantasia">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="cnpjContratada">CNPJ:</label>
                    <input type="text" id="cnpjContratada" name="cnpjContratada" required>
                </div>
                <div class="form-group">
                    <label for="ieContratada">Inscrição Estadual:</label>
                    <input type="text" id="ieContratada" name="ieContratada">
                </div>
            </div>

            <div class="form-group">
                <label for="enderecoContratada">Endereço Completo:</label>
                <input type="text" id="enderecoContratada" name="enderecoContratada" required>
            </div>

            <div class="form-group">
                <label for="nomeRepresentanteContratada">Nome do Representante:</label>
                <input type="text" id="nomeRepresentanteContratada" name="nomeRepresentanteContratada" required>
            </div>

            <!-- SERVIÇOS -->
            <div class="section-title">🔨 Detalhes dos Serviços</div>

            <div class="form-group">
                <label for="descricaoServicos">Descrição dos Serviços:</label>
                <textarea id="descricaoServicos" name="descricaoServicos" required placeholder="Descreva detalhadamente os serviços a serem prestados..."></textarea>
            </div>

            <div class="form-group">
                <label for="especificacaoTecnica">Especificação Técnica:</label>
                <textarea id="especificacaoTecnica" name="especificacaoTecnica" placeholder="Detalhes técnicos específicos..."></textarea>
            </div>

            <div class="form-group">
                <label for="localPrestacao">Local de Prestação:</label>
                <input type="text" id="localPrestacao" name="localPrestacao" required>
            </div>

            <!-- REGIME DE TRABALHO -->
            <div class="section-title">⏰ Regime e Prazos</div>

            <div class="form-row">
                <div class="form-group">
                    <label for="regimeTrabalho">Regime de Trabalho:</label>
                    <select id="regimeTrabalho" name="regimeTrabalho" required>
                        <option value="">Selecione...</option>
                        <option value="integral">Integral</option>
                        <option value="parcial">Parcial</option>
                        <option value="por demanda">Por Demanda</option>
                        <option value="emergencial">Emergencial</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="prazoPlanejamento">Prazo Planejamento:</label>
                    <input type="text" id="prazoPlanejamento" name="prazoPlanejamento" placeholder="Ex: 5 dias úteis">
                </div>
            </div>

            <div class="form-group">
                <label for="detalhamentoPeriodos">Detalhamento dos Períodos:</label>
                <textarea id="detalhamentoPeriodos" name="detalhamentoPeriodos" placeholder="Descreva os horários e períodos de trabalho..."></textarea>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="descricaoExecucao">Descrição da Execução:</label>
                    <input type="text" id="descricaoExecucao" name="descricaoExecucao" placeholder="Ex: Execução dos serviços">
                </div>
                <div class="form-group">
                    <label for="prazoExecucao">Prazo de Execução:</label>
                    <input type="text" id="prazoExecucao" name="prazoExecucao" placeholder="Ex: 30 dias corridos">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="dataInicio">Data de Início:</label>
                    <input type="date" id="dataInicio" name="dataInicio" required>
                </div>
                <div class="form-group">
                    <label for="dataTermino">Data de Término:</label>
                    <input type="date" id="dataTermino" name="dataTermino" required>
                </div>
            </div>

            <div class="form-group">
                <label for="prazoTotal">Prazo Total:</label>
                <input type="text" id="prazoTotal" name="prazoTotal" placeholder="Ex: 60 dias corridos">
            </div>

            <!-- VALORES -->
            <div class="section-title">💰 Valores e Pagamento</div>

            <div class="form-group">
                <label for="estruturaValores">Estrutura de Valores:</label>
                <textarea id="estruturaValores" name="estruturaValores" required placeholder="Descreva como os valores são estruturados..."></textarea>
            </div>

            <div class="form-group">
                <label for="valorTotal">Valor Total:</label>
                <input type="text" id="valorTotal" name="valorTotal" required placeholder="Ex: R$ 50.000,00">
            </div>

            <div class="form-group">
                <label for="estruturaCalculoPrecos">Estrutura de Cálculo de Preços:</label>
                <textarea id="estruturaCalculoPrecos" name="estruturaCalculoPrecos" placeholder="Como os preços são calculados..."></textarea>
            </div>

            <div class="form-group">
                <label for="tabelasPrecos">Tabelas de Preços:</label>
                <textarea id="tabelasPrecos" name="tabelasPrecos" placeholder="Tabelas ou listas de preços específicos..."></textarea>
            </div>

            <div class="form-group">
                <label for="detalhamentoCustosExtras">Custos Extras:</label>
                <textarea id="detalhamentoCustosExtras" name="detalhamentoCustosExtras" placeholder="Detalhamento de custos adicionais..."></textarea>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="formaPagamento">Forma de Pagamento:</label>
                    <select id="formaPagamento" name="formaPagamento" required>
                        <option value="">Selecione...</option>
                        <option value="À vista">À vista</option>
                        <option value="30 dias">30 dias</option>
                        <option value="30/60 dias">30/60 dias</option>
                        <option value="30/60/90 dias">30/60/90 dias</option>
                        <option value="Semanal">Semanal</option>
                        <option value="Quinzenal">Quinzenal</option>
                        <option value="Mensal">Mensal</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="prazoPagamento">Prazo para Pagamento:</label>
                    <input type="text" id="prazoPagamento" name="prazoPagamento" required placeholder="Ex: 30 dias após faturamento">
                </div>
            </div>

            <!-- OBRIGAÇÕES ESPECÍFICAS -->
            <div class="section-title">📝 Obrigações Específicas</div>

            <div class="form-group">
                <label for="metasEspecificas">Metas Específicas:</label>
                <textarea id="metasEspecificas" name="metasEspecificas" placeholder="Metas ou objetivos específicos do contrato..."></textarea>
            </div>

            <div class="form-group">
                <label for="atividadesAdicionais">Atividades Adicionais:</label>
                <textarea id="atividadesAdicionais" name="atividadesAdicionais" placeholder="Atividades extras ou complementares..."></textarea>
            </div>

            <div class="form-group">
                <label for="responsabilidadeFerramentas">Responsabilidade por Ferramentas:</label>
                <textarea id="responsabilidadeFerramentas" name="responsabilidadeFerramentas" placeholder="Quem fornece ferramentas, equipamentos, etc..."></textarea>
            </div>

            <div class="form-group">
                <label for="obrigacoesEspecificasContratante">Obrigações Específicas da Contratante:</label>
                <textarea id="obrigacoesEspecificasContratante" name="obrigacoesEspecificasContratante" placeholder="Obrigações específicas da Orkan..."></textarea>
            </div>

            <!-- ANEXOS E FINALIZAÇÃO -->
            <div class="section-title">📎 Anexos e Finalização</div>

            <div class="form-group">
                <label for="anexos">Anexos:</label>
                <textarea id="anexos" name="anexos" placeholder="Liste os anexos do contrato..."></textarea>
            </div>

            <div class="form-group">
                <label for="dataAssinatura">Data de Assinatura:</label>
                <input type="date" id="dataAssinatura" name="dataAssinatura" required>
            </div>

            <button type="button" class="btn" id="preencherTesteBtn" style="background: #28a745; margin-bottom: 10px;">
                🎲 Preencher Dados de Teste
            </button>

            <button type="submit" class="btn" id="gerarBtn">
                📄 Gerar Contrato PDF
            </button>

            <div class="loading" id="loading">
                ⏳ Gerando PDF... Aguarde...
            </div>

            <div class="error" id="error"></div>
            <div class="success" id="success"></div>
        </form>
    </div>

    <script>
        // Auto-preencher data de assinatura com hoje
        document.addEventListener('DOMContentLoaded', function() {
            const hoje = new Date().toISOString().split('T')[0];
            document.getElementById('dataAssinatura').value = hoje;
        });

        // Função para preencher dados de teste
        document.getElementById('preencherTesteBtn').addEventListener('click', function() {
            // Dados da Contratada
            document.getElementById('nomeContratada').value = 'TECH SOLUTIONS BRASIL LTDA';
            document.getElementById('nomeFantasia').value = 'TechSol Brasil';
            document.getElementById('cnpjContratada').value = '12.345.678/0001-90';
            document.getElementById('ieContratada').value = '123.456.789.012';
            document.getElementById('enderecoContratada').value = 'Rua das Tecnologias, nº 123, Sala 45, Centro Empresarial Tech, CEP 01234-567, São Paulo - SP';
            document.getElementById('nomeRepresentanteContratada').value = 'João Carlos Silva Santos';

            // Serviços
            document.getElementById('descricaoServicos').value = 'Prestação de serviços especializados em manutenção preventiva e corretiva de equipamentos eletrônicos, incluindo diagnóstico, reparo, substituição de componentes e testes de qualidade em equipamentos de informática, telecomunicações e automação industrial.';
            document.getElementById('especificacaoTecnica').value = 'Serviços técnicos especializados em: 1) Manutenção de computadores e servidores; 2) Reparo de equipamentos de rede; 3) Instalação e configuração de sistemas; 4) Suporte técnico especializado; 5) Testes e certificação de equipamentos.';
            document.getElementById('localPrestacao').value = 'Nas dependências da CONTRATANTE, localizada na Rua Jônio, nº 64, Jardim do Mar, São Bernardo do Campo - SP, e eventualmente em campo conforme demanda técnica.';

            // Regime e Prazos
            document.getElementById('regimeTrabalho').value = 'integral';
            document.getElementById('prazoPlanejamento').value = '5 dias úteis';
            document.getElementById('detalhamentoPeriodos').value = 'Horário comercial de segunda a sexta-feira, das 08h00 às 17h00, com possibilidade de atendimento emergencial 24h mediante solicitação prévia e acordo específico.';
            document.getElementById('descricaoExecucao').value = 'Execução completa dos serviços técnicos';
            document.getElementById('prazoExecucao').value = '45 dias corridos';

            // Datas
            const hoje = new Date();
            const dataInicio = new Date(hoje);
            dataInicio.setDate(hoje.getDate() + 7);
            const dataTermino = new Date(dataInicio);
            dataTermino.setDate(dataInicio.getDate() + 45);

            document.getElementById('dataInicio').value = dataInicio.toISOString().split('T')[0];
            document.getElementById('dataTermino').value = dataTermino.toISOString().split('T')[0];
            document.getElementById('prazoTotal').value = '60 dias corridos (incluindo planejamento e execução)';

            // Valores
            document.getElementById('estruturaValores').value = 'Valor fixo mensal de R$ 15.000,00 para serviços básicos + valores variáveis conforme demanda específica: Manutenção preventiva: R$ 200,00/equipamento; Manutenção corretiva: R$ 350,00/equipamento; Atendimento emergencial: R$ 150,00/hora.';
            document.getElementById('valorTotal').value = 'R$ 45.000,00 (quarenta e cinco mil reais)';
            document.getElementById('estruturaCalculoPrecos').value = 'Preços baseados em tabela técnica especializada, considerando complexidade do equipamento, tempo estimado de reparo e custo de componentes necessários.';
            document.getElementById('tabelasPrecos').value = 'Tabela A: Equipamentos básicos (R$ 200-400); Tabela B: Equipamentos intermediários (R$ 400-800); Tabela C: Equipamentos complexos (R$ 800-1500).';
            document.getElementById('detalhamentoCustosExtras').value = 'Custos de componentes e peças de reposição por conta da CONTRATANTE. Deslocamentos superiores a 50km: R$ 2,00/km. Trabalhos em finais de semana: acréscimo de 50% sobre valor/hora.';
            document.getElementById('formaPagamento').value = '30/60 dias';
            document.getElementById('prazoPagamento').value = '30 dias após apresentação da nota fiscal';

            // Obrigações Específicas
            document.getElementById('metasEspecificas').value = 'Garantir 95% de disponibilidade dos equipamentos críticos; Tempo máximo de resposta para emergências: 4 horas; Relatórios mensais de manutenção preventiva.';
            document.getElementById('atividadesAdicionais').value = 'Treinamento básico da equipe da CONTRATANTE; Elaboração de manuais técnicos simplificados; Consultoria para aquisição de novos equipamentos.';
            document.getElementById('responsabilidadeFerramentas').value = 'CONTRATADA fornece todas as ferramentas especializadas e equipamentos de teste. CONTRATANTE fornece acesso às instalações e equipamentos básicos de apoio.';
            document.getElementById('obrigacoesEspecificasContratante').value = 'Fornecer acesso irrestrito aos equipamentos; Disponibilizar técnico acompanhante quando solicitado; Comunicar falhas com antecedência mínima de 2 horas.';

            // Anexos
            document.getElementById('anexos').value = 'Anexo I - Especificações técnicas detalhadas; Anexo II - Lista de equipamentos cobertos; Anexo III - Tabela de preços atualizada; Anexo IV - Cronograma de manutenções preventivas.';

            alert('✅ Dados de teste preenchidos com sucesso! Agora você pode gerar o PDF.');
        });

        // Função para formatar CNPJ
        document.getElementById('cnpjContratada').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = value.replace(/^(\d{2})(\d)/, '$1.$2');
            value = value.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
            value = value.replace(/\.(\d{3})(\d)/, '.$1/$2');
            value = value.replace(/(\d{4})(\d)/, '$1-$2');
            e.target.value = value;
        });

        // Handler do formulário
        document.getElementById('contratoForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const btn = document.getElementById('gerarBtn');
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const success = document.getElementById('success');

            // Reset estados
            error.style.display = 'none';
            success.style.display = 'none';
            btn.disabled = true;
            loading.style.display = 'block';

            try {
                // Coletar dados do formulário
                const formData = new FormData(e.target);
                const dados = Object.fromEntries(formData.entries());

                // Validar campos obrigatórios
                const camposObrigatorios = [
                    'nomeContratada', 'cnpjContratada', 'enderecoContratada',
                    'nomeRepresentanteContratada', 'descricaoServicos',
                    'localPrestacao', 'regimeTrabalho', 'dataInicio',
                    'dataTermino', 'estruturaValores', 'valorTotal',
                    'formaPagamento', 'prazoPagamento', 'dataAssinatura'
                ];

                for (let campo of camposObrigatorios) {
                    if (!dados[campo] || dados[campo].trim() === '') {
                        throw new Error(`Campo "${campo}" é obrigatório`);
                    }
                }

                // Enviar para o servidor
                const response = await fetch('/gerar-pdf', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(dados)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'Erro ao gerar PDF');
                }

                // Download do PDF
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `contrato_${dados.nomeContratada.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                success.textContent = '✅ PDF gerado com sucesso!';
                success.style.display = 'block';

            } catch (err) {
                console.error('Erro:', err);
                error.textContent = `❌ Erro: ${err.message}`;
                error.style.display = 'block';
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
            }
        });
    </script>
</body>
</html>